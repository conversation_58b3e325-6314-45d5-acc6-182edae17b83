<template>
  <view class="certificate-modal-overlay" @click.self="closeModal">
    <view class="modal-content">
      <view class="close-button-wrapper" @click="closeModal">
        <view class="close-button">
          <text class="close-button-text"> 返回 </text>
        </view>
      </view>

      <view class="certificate-container">
        <image class="certificate-bg" src="@/static/certificate-bg.jpg" mode="aspectFill" />
        <view class="certificate-content">
          <text class="certificate-title">认养证书</text>
          <text class="certificate-id">证书编号：{{ certificateId }}</text>

          <view class="adoptee-name-wrapper">
            <text class="adoptee-name">{{ userName }}</text>
            <view class="name-underline"></view>
          </view>

          <view class="certificate-body">
            <!-- 第一段：基本信息 -->
            <view class="cert-paragraph">
              <text class="text-segment"> 恭喜您，成功认养了位于 </text>
              <text class="text-segment-bold"> {{ location }} </text>
              <text class="text-segment"> 的 </text>
              <text class="text-segment-bold"> {{ fruitTreeName }} </text>
              <text class="text-segment"> 编号为 </text>
              <text class="text-segment-red"> {{ certificateId }} </text>
              <text class="text-segment">。</text>
            </view>

            <!-- 第二段：证书内容 -->
            <view class="cert-paragraph">
              <text class="text-segment">{{ certContent }}</text>
            </view>

            <!-- 发证机构和日期 -->
            <view class="cert-footer">
              <text class="text-segment">{{ issuerName }}</text>
              <text class="text-segment">{{ formattedDate }}</text>
            </view>
          </view>
        </view>
      </view>

      <text class="save-tip"> 长按图片保存到手机 </text>
    </view>
  </view>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

const emit = defineEmits(['close'])

const props = defineProps({
  adoption: {
    type: Object,
    required: true
  }
})

const userStore = useUserStore()

const closeModal = () => {
  emit('close')
}

// 组件挂载时确保用户信息已加载
onMounted(async () => {
  // 如果用户信息不存在，尝试获取
  if (!userStore.userInfo) {
    try {
      await userStore.refreshUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})

// 计算属性：用户昵称
const userName = computed(() => {
  return props.adoption?.certUserName || userStore.userInfo?.nickName || '认养用户'
})

// 计算属性：证书编号
const certificateId = computed(() => {
  return props.adoption?.certCode || props.adoption?.orderSn || props.adoption?.id || 'CERT' + Date.now()
})

// 计算属性：果树名称
const fruitTreeName = computed(() => {
  return props.adoption?.certVariety || props.adoption?.fruitTreeName || '果树'
})

// 计算属性：地点
const location = computed(() => {
  return props.adoption?.certOrigin || '陕西延安'
})

// 计算属性：格式化的日期
const formattedDate = computed(() => {
  return props.adoption?.certIssueDate || (() => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    return `${year}年${month}月${day}日`
  })()
})

// 计算属性：发证机构
const issuerName = computed(() => {
  return props.adoption?.certIssuerName || '金壶口生态基金会'
})

// 计算属性：证书内容
const certContent = computed(() => {
  return props.adoption?.certContent || '绿荫泽被，生生不息。您的认养善举，不仅赋予了这棵树木特别的守护，更是为城市增添一抹生机，为大地播撒一份希望。感谢您对绿色家园的深情厚意，您的行动让我们的环境更加清新美好。'
})
</script>

<style lang="scss" scoped>
.certificate-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.close-button-wrapper {
  margin-bottom: 32rpx;
  align-self: flex-start;
  margin-left: 75rpx;
}

.close-button {
  width: 166rpx;
  height: 64rpx;
  border-radius: 326rpx;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-button-text {
  font-size: 30rpx;
  color: #ffffff;
}

.certificate-container {
  width: 600rpx;
  height: 900rpx;
  position: relative;
}

.certificate-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.certificate-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.certificate-title {
  margin-top: 64rpx;
  font-size: 64rpx;
  font-weight: 700;
  color: #dd3c29;
  line-height: 75rpx;
}

.certificate-id {
  margin-top: 16rpx;
  font-size: 22rpx;
  color: #dd3c29;
  line-height: 26rpx;
}

.adoptee-name-wrapper {
  margin-top: 56rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.adoptee-name {
  font-size: 36rpx;
  font-weight: 500;
  color: #1a1a1a;
  line-height: 42rpx;
}

.name-underline {
  width: 360rpx;
  height: 2rpx;
  background-color: rgba(221, 60, 41, 0.5);
  margin-top: 31rpx;
}

.certificate-body {
  margin-top: 31rpx;
  width: 436rpx;
  line-height: 40rpx;
  color: #3d3d3d;
}

.cert-paragraph {
  margin-bottom: 20rpx;
  text-align: left;
}

.cert-footer {
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10rpx;
  text-align: left;
}

.text-segment {
  font-size: 22rpx;
}
.text-segment-bold {
  font-size: 22rpx;
  font-weight: 700;
}
.text-segment-red {
  font-size: 22rpx;
  color: #dd3c29;
}

.save-tip {
  margin-top: 32rpx;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 28rpx;
}
</style>